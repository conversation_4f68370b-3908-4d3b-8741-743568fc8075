package main

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// 请求消息结构
type RequestMessage struct {
	Type     string `json:"type"`
	UidFrom  string `json:"uid_from"`
	UserType int    `json:"user_type"`
	Code     int    `json:"code"`
	Sign     string `json:"sign"`
	Msg      string `json:"msg"`
}

// sendMsg消息中的msg字段结构
type SendMsgData struct {
	ChouIndex string `json:"chouIndex"`
	ChouMsg   string `json:"chouMsg"`
	Message   string `json:"message"`
}

// 响应消息结构
type ResponseMessage struct {
	Code     int         `json:"code"`
	Msg      string      `json:"msg"`
	MsgType  string      `json:"msg_type"`
	ClientID string      `json:"client_id"`
	Data     interface{} `json:"data"`
}

// 客户端连接结构
type Client struct {
	ID     string
	UserID string // 用户设备标识符，来自reg消息的uid_from
	Conn   *websocket.Conn
	Send   chan ResponseMessage
}

// 网页端连接结构
type WebClient struct {
	ID     string
	UserID string // 用户设备标识符，来自web消息的uid_from
	Conn   *websocket.Conn
	Send   chan ResponseMessage
	Type   string // 标识为"web"类型
}

// 连接管理器
type Hub struct {
	clients    map[string]*Client
	webClients map[string]*WebClient // 网页端客户端管理
	register   chan *Client
	unregister chan *Client
	webRegister   chan *WebClient    // 网页端注册通道
	webUnregister chan *WebClient    // 网页端注销通道
	mutex      sync.RWMutex
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源
	},
}

// AES-CBC-128解密函数
func decryptAES(encryptedData, key, iv string) (string, error) {
	// Base64解码加密数据
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %v", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 检查IV长度
	if len(iv) != aes.BlockSize {
		return "", fmt.Errorf("IV长度不正确，应为%d字节", aes.BlockSize)
	}

	// 检查密文长度
	if len(ciphertext) < aes.BlockSize || len(ciphertext)%aes.BlockSize != 0 {
		return "", fmt.Errorf("密文长度不正确")
	}

	// 创建CBC模式解密器
	mode := cipher.NewCBCDecrypter(block, []byte(iv))

	// 解密
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// 去除PKCS7填充
	plaintext = removePKCS7Padding(plaintext)

	return string(plaintext), nil
}

// 去除PKCS7填充
func removePKCS7Padding(data []byte) []byte {
	if len(data) == 0 {
		return data
	}
	padding := int(data[len(data)-1])
	if padding > len(data) || padding > aes.BlockSize {
		return data
	}
	return data[:len(data)-padding]
}

func newHub() *Hub {
	return &Hub{
		clients:    make(map[string]*Client),
		webClients: make(map[string]*WebClient),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		webRegister:   make(chan *WebClient),
		webUnregister: make(chan *WebClient),
	}
}

func (h *Hub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client.ID] = client
			h.mutex.Unlock()
			log.Printf("客户端 %s 已连接，当前连接数: %d", client.ID, len(h.clients))

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client.ID]; ok {
				delete(h.clients, client.ID)
				close(client.Send)
				log.Printf("客户端 %s 已断开，当前连接数: %d", client.ID, len(h.clients))
			}
			h.mutex.Unlock()

		case webClient := <-h.webRegister:
			h.mutex.Lock()
			h.webClients[webClient.ID] = webClient
			h.mutex.Unlock()
			log.Printf("网页端客户端 %s 已连接，当前网页端连接数: %d", webClient.ID, len(h.webClients))

		case webClient := <-h.webUnregister:
			h.mutex.Lock()
			if _, ok := h.webClients[webClient.ID]; ok {
				delete(h.webClients, webClient.ID)
				close(webClient.Send)
				log.Printf("网页端客户端 %s 已断开，当前网页端连接数: %d", webClient.ID, len(h.webClients))
			}
			h.mutex.Unlock()
		}
	}
}

func (h *Hub) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 生成客户端ID
	clientID := fmt.Sprintf("%s", r.RemoteAddr)
	
	// 先创建一个临时的普通客户端来接收第一条消息
	tempClient := &Client{
		ID:   clientID,
		Conn: conn,
		Send: make(chan ResponseMessage, 256),
	}

	// 启动临时的消息处理来判断客户端类型
	go h.handleClientTypeDetection(tempClient)
}

func (h *Hub) handleClientTypeDetection(client *Client) {
	// 设置读取超时
	client.Conn.SetReadDeadline(time.Now().Add(30 * time.Second))

	// 等待第一条消息来判断客户端类型
	messageType, message, err := client.Conn.ReadMessage()
	if err != nil {
		log.Printf("读取客户端类型检测消息失败: %v", err)
		client.Conn.Close()
		return
	}

	if messageType == websocket.TextMessage {
		var msg RequestMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("解析类型检测消息失败: %v", err)
			client.Conn.Close()
			return
		}

		if msg.Type == "web" {
			// 创建网页端客户端
			webClient := &WebClient{
				ID:     client.ID,
				UserID: msg.UidFrom,
				Conn:   client.Conn,
				Send:   make(chan ResponseMessage, 256),
				Type:   "web",
			}

			log.Printf("检测到网页端客户端: %s, 设备标识: %s", webClient.ID, webClient.UserID)

			// 注册网页端客户端
			h.webRegister <- webClient

			// 发送注册响应
			response := ResponseMessage{
				Code:     999,
				Msg:      "",
				MsgType:  "sys",
				ClientID: "7f000001140a0013084c",
				Data:     []interface{}{},
			}

			select {
			case webClient.Send <- response:
			case <-time.After(5 * time.Second):
			}

			// 启动网页端客户端的消息处理
			go h.webWritePump(webClient)
			go h.webReadPump(webClient)

		} else {
			// 创建普通客户端
			client.UserID = msg.UidFrom
			log.Printf("检测到普通客户端: %s, 设备标识: %s", client.ID, client.UserID)

			// 注册普通客户端
			h.register <- client

			// 处理这条消息
			response := h.processMessage(msg, client.ID)
			select {
			case client.Send <- response:
			case <-time.After(5 * time.Second):
			}

			// 启动普通客户端的消息处理
			go h.writePump(client)
			go h.readPump(client)
		}
	} else {
		log.Printf("收到非文本消息，关闭连接")
		client.Conn.Close()
	}
}

// 网页端客户端读取消息
func (h *Hub) webReadPump(webClient *WebClient) {
	defer func() {
		h.webUnregister <- webClient
		webClient.Conn.Close()
	}()

	// 设置读取超时和pong处理
	webClient.Conn.SetReadDeadline(time.Now().Add(300 * time.Second))
	webClient.Conn.SetPongHandler(func(string) error {
		webClient.Conn.SetReadDeadline(time.Now().Add(300 * time.Second))
		return nil
	})

	for {
		messageType, message, err := webClient.Conn.ReadMessage()
		if err != nil {
			break
		}

		switch messageType {
		case websocket.TextMessage:
			var msg RequestMessage
			if err := json.Unmarshal(message, &msg); err != nil {
				log.Printf("网页端消息解析失败: %v", err)
				continue
			}

			log.Printf("网页端客户端 %s 发送消息: %s", webClient.ID, string(message))

			// 处理消息
			response := h.processMessage(msg, webClient.ID)

			// 发送响应
			select {
			case webClient.Send <- response:
			case <-time.After(5 * time.Second):
			}

		case websocket.PingMessage:
			log.Printf("网页端客户端 %s 发送了 Ping 消息", webClient.ID)
		case websocket.PongMessage:
			log.Printf("网页端客户端 %s 发送了 Pong 消息", webClient.ID)
		}
	}
}

// 网页端客户端写入消息
func (h *Hub) webWritePump(webClient *WebClient) {
	defer webClient.Conn.Close()

	// 设置写入超时
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case message, ok := <-webClient.Send:
			webClient.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				webClient.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := webClient.Conn.WriteJSON(message); err != nil {
				return
			}
		case <-ticker.C:
			webClient.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := webClient.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (h *Hub) writePump(client *Client) {
	defer client.Conn.Close()

	// 设置写入超时
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := client.Conn.WriteJSON(message); err != nil {
				return
			}
		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// 发送消息到对应的网页端客户端
func (h *Hub) sendToWebClients(userID, message string) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	// 遍历所有网页端客户端，找到匹配的UserID
	for _, webClient := range h.webClients {
		if webClient.UserID == userID {
			response := ResponseMessage{
				Code:     0,
				Msg:      "收到新消息",
				MsgType:  "message",
				ClientID: webClient.ID,
				Data:     message,
			}
			
			select {
			case webClient.Send <- response:
				log.Printf("消息已发送到网页端客户端 %s (设备标识: %s)", webClient.ID, userID)
			case <-time.After(5 * time.Second):
				log.Printf("发送消息到网页端客户端 %s 超时", webClient.ID)
			}
		}
	}
}

func (h *Hub) processMessage(msg RequestMessage, clientID string) ResponseMessage {
	switch msg.Type {
	case "reg":
		return ResponseMessage{
			Code:     999,
			Msg:      "",
			MsgType:  "sys",
			ClientID: "7f000001140a0013084c",
			Data:     []interface{}{},
		}
	case "web":
		return ResponseMessage{
			Code:     999,
			Msg:      "",
			MsgType:  "sys",
			ClientID: "7f000001140a0013084c",
			Data:     []interface{}{},
		}
	case "sendMsg":
		return ResponseMessage{
			Code:     0,
			Msg:      "没有相关的记录",
			MsgType:  "sys",
			ClientID: "7f000001140a0013084c",
			Data:     []interface{}{},
		}
	default:
		return ResponseMessage{
			Code:     -1,
			Msg:      "未知的消息类型",
			MsgType:  "sys",
			ClientID: "7f000001140a0013084c",
			Data:     []interface{}{},
		}
	}
}

func (h *Hub) readPump(client *Client) {
	defer func() {
		h.unregister <- client
		client.Conn.Close()
	}()

	// 设置读取超时和pong处理
	client.Conn.SetReadDeadline(time.Now().Add(300 * time.Second))
	client.Conn.SetPongHandler(func(string) error {
		client.Conn.SetReadDeadline(time.Now().Add(300 * time.Second))
		return nil
	})

	for {
		messageType, message, err := client.Conn.ReadMessage()
		if err != nil {
			break
		}

		switch messageType {
		case websocket.TextMessage:
			// 检查是否为简单的文本命令
			messageStr := string(message)
			if messageStr == "ping" {
				log.Printf("客户端 %s 发送了文本ping消息", client.ID)
				// 可以选择回复pong或者忽略
				response := ResponseMessage{
					Code:     0,
					Msg:      "pong",
					MsgType:  "sys",
					ClientID: client.ID,
					Data:     []interface{}{},
				}
				select {
				case client.Send <- response:
				case <-time.After(5 * time.Second):
				}
				continue
			}
			
			var msg RequestMessage
			if err := json.Unmarshal(message, &msg); err != nil {
				log.Printf("消息解析失败: %v, 原始消息内容: %q", err, string(message))
				// 发送错误响应
				response := ResponseMessage{
					Code:     -1,
					Msg:      "消息格式错误，请发送有效的JSON格式",
					MsgType:  "error",
					ClientID: client.ID,
					Data:     []interface{}{},
				}
				select {
				case client.Send <- response:
				case <-time.After(5 * time.Second):
				}
				continue
			}

			// 只有当消息类型为reg时才打印发送的消息
			if msg.Type == "reg" {
				log.Printf("客户端 %s 发送的消息: %s", client.ID, string(message))
				// 保存用户设备标识符
				h.mutex.Lock()
				if client, exists := h.clients[client.ID]; exists {
					client.UserID = msg.UidFrom
					log.Printf("设备注册成功 - 连接ID: %s, 设备标识: %s", client.ID, client.UserID)
				}
				h.mutex.Unlock()
			}

			// 如果是sendMsg类型，解析并打印message值
			if msg.Type == "sendMsg" {
				var sendMsgData SendMsgData
				if err := json.Unmarshal([]byte(msg.Msg), &sendMsgData); err != nil {
					log.Printf("解析sendMsg中的msg字段失败: %v, msg内容: %s", err, msg.Msg)
				} else {
					// 获取设备标识符
					h.mutex.RLock()
					userID := "未知设备"
					if client, exists := h.clients[client.ID]; exists && client.UserID != "" {
						userID = client.UserID
					}
					h.mutex.RUnlock()
					
					// AES解密参数
					key := "2f6e7631419a165b"
					iv := "b561a9141367e6f2"
					
					// 解密message值
					decryptedMessage, err := decryptAES(sendMsgData.Message, key, iv)
					if err != nil {
						log.Printf("设备 %s 解密message失败: %v, 原始message: %s", userID, err, sendMsgData.Message)
					} else {
						// 对解密后的结果再进行Base64解码
						finalMessage, err := base64.StdEncoding.DecodeString(decryptedMessage)
						if err != nil {
							log.Printf("设备 %s Base64解码失败: %v, 解密后内容: %s", userID, err, decryptedMessage)
						} else {
							//log.Printf("设备 %s 最终解码后的message: %s", userID, string(finalMessage))
							
							// 将解码后的数据发送到对应的网页端客户端
							h.sendToWebClients(userID, string(finalMessage))
						}
					}
				}
			}

			// 处理消息
			response := h.processMessage(msg, client.ID)

			// 发送响应
			select {
			case client.Send <- response:
			case <-time.After(5 * time.Second):
			}
		case websocket.PingMessage:
			log.Printf("客户端 %s 发送了 Ping 消息", client.ID)
		case websocket.PongMessage:
			log.Printf("客户端 %s 发送了 Pong 消息", client.ID)
		default:
			log.Printf("客户端 %s 发送了未知类型的消息: %d", client.ID, messageType)
		}
	}
}

func main() {
	hub := newHub()
	go hub.run()

	http.HandleFunc("/", hub.handleWebSocket)



	log.Println("WebSocket服务器启动在端口 5311")
	log.Fatal(http.ListenAndServe(":5311", nil))
}
